/********* web content *********/
/* add padding */
#webview-container {
    padding: 0px 10px 10px 10px!important;
    box-shadow: none!important;
}
/* add border radius */
#webpage-stack {
    border-radius: 10px!important;
}
/*remove padding and border radius for videos in full screen*/
.fullscreen .inner #webview-container {
    padding: 0px!important;
}

.fullscreen .inner #webpage-stack {
    border-radius: 0px!important;
}

/************ main toolbar moved to left sidebar top **************/
/* Hide the original mainbar position */
.mainbar {
    display: none !important;
}

/* Create new address bar container in left sidebar */
#tabs-tabbar-container::before {
    content: '';
    display: block;
    position: relative;
    width: 100%;
    height: 40px;
    background: var(--colorBgDark);
    border-radius: 8px;
    margin-bottom: 10px;
    z-index: 100;
}

/* Move the actual toolbar to the sidebar with ARC-style positioning */
.toolbar-mainbar {
    position: fixed !important;
    top: 20px !important;
    left: 20px !important;
    width: calc(var(--tabBarWidth, 250px) - 40px) !important;
    height: 40px !important;
    background: rgba(255, 255, 255, 0.08) !important;
    backdrop-filter: blur(20px) !important;
    border-radius: 12px !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    z-index: 101 !important;
    padding: 6px 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3),
                0 1px 3px rgba(0, 0, 0, 0.2) !important;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1) !important;
    transform: translateY(0) !important;
}

/* ARC-style hover effect */
.toolbar-mainbar:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4),
                0 2px 6px rgba(0, 0, 0, 0.3) !important;
    background: rgba(255, 255, 255, 0.12) !important;
}

/* Style the URL field with ARC-like design */
.toolbar-mainbar .UrlBar-AddressField {
    background: rgba(255, 255, 255, 0.06) !important;
    border-radius: 8px !important;
    border: 1px solid rgba(255, 255, 255, 0.08) !important;
    color: rgba(255, 255, 255, 0.9) !important;
    font-size: 13px !important;
    font-weight: 400 !important;
    height: 28px !important;
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1) !important;
    backdrop-filter: blur(10px) !important;
}

.toolbar-mainbar .UrlBar-AddressField:focus,
.toolbar-mainbar .UrlBar-AddressField:focus-within {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1) !important;
    transform: scale(1.02) !important;
}

/* Style buttons in the relocated toolbar with ARC aesthetics */
.toolbar-mainbar .button-toolbar {
    border-radius: 8px !important;
    background: rgba(255, 255, 255, 0.06) !important;
    color: rgba(255, 255, 255, 0.8) !important;
    height: 28px !important;
    min-width: 28px !important;
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1) !important;
    backdrop-filter: blur(10px) !important;
}

.toolbar-mainbar .button-toolbar:hover {
    background: rgba(255, 255, 255, 0.12) !important;
    color: rgba(255, 255, 255, 1) !important;
    border-color: rgba(255, 255, 255, 0.15) !important;
    transform: scale(1.05) !important;
}

.toolbar-mainbar .button-toolbar:active {
    transform: scale(0.98) !important;
    background: rgba(255, 255, 255, 0.08) !important;
}

/* ARC-style auto-hide functionality */
.toolbar-mainbar:not(:hover):not(:focus-within):not(.focused) {
    opacity: 0.7 !important;
    transform: translateY(-5px) scale(0.98) !important;
    transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1) !important;
}

/* Show on hover with smooth animation */
.toolbar-mainbar:hover,
.toolbar-mainbar:focus-within,
.toolbar-mainbar.focused {
    opacity: 1 !important;
    transform: translateY(0) scale(1) !important;
}

/* Hide toolbar extensions in the new compact layout */
.toolbar-extensions .button-toolbar {
    display: none;
}

/* Improve the visual integration with sidebar */
.toolbar-mainbar::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.02) 0%,
                rgba(255, 255, 255, 0.01) 100%);
    border-radius: 16px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.toolbar-mainbar:hover::before {
    opacity: 1;
}

/* Responsive adjustments for different sidebar widths */
@media (max-width: 1200px) {
    .toolbar-mainbar {
        width: calc(var(--tabBarWidth, 200px) - 40px) !important;
        font-size: 12px !important;
    }
}

/* Ensure proper stacking order */
.toolbar-mainbar {
    z-index: 999 !important;
}

/* Adjust main content area to account for relocated address bar */
#main {
    padding-top: 0 !important;
}

/* Hide original address bar space completely */
.address-top .mainbar {
    height: 0 !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Adjust account button */
.mainbar .button-toolbar button[name=AccountButton] img {
    padding: 2px!important
}

.tilingtoggle rect {
    opacity: 1
}

/*************** tabs panel on left ***************/
/* styles will be broken for other tab bar position */

.inner > div.tabbar-wrapper {
    padding-left: 10px;
    padding-bottom: 10px;
    padding-top: 70px !important; /* Make space for relocated address bar */
}

#tabs-tabbar-container {
    background: none!important;
    padding-top: 70px!important; /* Increased to accommodate address bar */
    backdrop-filter: none!important;
    position: relative !important;
}

.tabbar-workspace-button {
    background: none!important;
    box-shadow: none!important;
}

.button-toolbar.newtab {
    display: none
}

.separator-wsbutton {
    display: none;
}

.tabbar-workspace-button > button {
    background: rgba(255, 255, 255, 0.141);
    color: #fff!important;
    font-weight: 700;
}

.toggle-trash .button-icon {
    color: #fff !important;
}

.tab {
    color: #fff!important;
    font-weight: 600;
}

.svg-tab-stack .stack-frame {
    stroke: #fff;
    stroke-width: 2px;
}

.accordion-toggle-arrow {
    color: #fff!important;
}

#tabs-tabbar-container > .SlideBar--FullHeight {
    right: -14px; 
}

#tabs-subcontainer {
    box-shadow: none !important;
    background: none !important;
}

.is-pinned .tab-header {
    padding-left: 5px !important
}

#tabs-subcontainer .tab-position {
    min-width: 32.5px
}

.tab-position .title {
    mask: none!important;
    margin-right: 8px!important;
}

.tab-group-indicator {
    right: 0px!important;
    left: 0px!important
}

.tab-position .svg-tab-stack {
    /* display: none!important */
}

/************* side panel on right ****************/
/* styles will be broken for other panel position */

/* uncomment this to not show side panel when minimised */
#panels-container.icons {
    /* display: none */
}

#panels-container {
    background: none;
    left: -5px;
    border: none !important;
}

.panel-group {
    border: none !important;
}

#panels-container .SlideBar--FullHeight {
    left: -10px; 
}

.toolbar-panel {
    padding-bottom: 10px
}

.panel.webpanel.visible {
    border-radius: 8px;
}

.webpanel-content {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

.webpanel-header {
    background: var(--colorBgDark);
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

#panels-container {
    padding-bottom: 10px
}

/************ bookmarks bar **************/
/* you can change the bookmarks bar position */
.bookmark-bar {
    background: none!important;
    height: 26px!important;
    position: relative;
    border-radius: 6px;
    border: none;
}

.bookmark-bar .observer{
    justify-content: center;
    padding: 0px!important;
}

.bookmark-bar .observer button{
    background: var(--colorBgIntense);
}

.bookmark-bar .observer button {
    border-top: 1px solid var(--colorBorder);
    border-bottom: 1px solid var(--colorBorder);
}

.bookmark-bar .observer button:first-child {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
    border: 1px solid var(--colorBorder);
    border-right: none
}

.bookmark-bar .observer button:last-child {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    border: 1px solid var(--colorBorder);
    border-left: none
}

.bookmark-bar .folder-icon {
    display: none
}

/*************** footer *****************/
footer {
    background: none!important;
    top: -3px;
    padding: 0px 5px
}

footer .toolbar-statusbar {
    height: unset!important;
}

/* **************** */

.tile {
    box-shadow: none!important
}
